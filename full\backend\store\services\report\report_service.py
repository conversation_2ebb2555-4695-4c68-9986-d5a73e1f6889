from django.db.models import <PERSON>, <PERSON><PERSON>, F, Decimal<PERSON>ield, ExpressionWrapper
from django.db.models.functions import Coalesce, ExtractMonth
from django.utils import timezone
from ...models import Order
from ...repositories.report.report_repository import ReportRepository
from .report_service_comparison import process_month_comparison, process_quarter_comparison
from .same_period_comparison import (
    _process_same_period_year_comparison,
    _process_same_period_month_comparison,
    _process_same_period_quarter_comparison
)

class ReportService:
    """
    Service class for handling business logic related to reports
    """

    @staticmethod
    def generate_revenue_report(date_from, date_to, sales_admin=None, delivery_staff=None):
        """
        Generate a revenue report with:
        - Total revenue
        - Total number of orders
        - Average order value
        - Number of pending orders
        - Revenue by date
        """
        # Parse date parameters
        start_date, end_date = ReportRepository.parse_date_params(date_from, date_to)

        if not start_date or not end_date:
            return None, "Invalid date format. Expected format is YYYY-MM-DD."

        # Get base queryset for revenue calculations
        queryset = ReportRepository.get_base_queryset(
            start_date, end_date, status_filter='delivered', include_all=False
        )

        # Get processing orders count
        processing_orders = ReportRepository.get_processing_orders_count(start_date, end_date)

        # Filter by sales admin if provided (supports multiple values)
        if sales_admin:
            # Handle comma-separated values
            if ',' in sales_admin:
                sales_admin_ids = [id.strip() for id in sales_admin.split(',') if id.strip()]
                queryset = queryset.filter(sales_admin_id__in=sales_admin_ids)
            else:
                queryset = queryset.filter(sales_admin_id=sales_admin)

        # Filter by delivery staff if provided
        if delivery_staff:
            queryset = queryset.filter(delivery_staff_id=delivery_staff)

        # Calculate aggregates
        aggregates = ReportRepository.get_revenue_aggregates(queryset)

        # Get daily revenue data
        daily_revenue = ReportRepository.get_daily_revenue(queryset)

        # Prepare report data
        report_data = {
            'total_revenue': float(aggregates['total_revenue']),
            'total_orders': aggregates['total_orders'],
            'average_order_value': float(aggregates['avg_order_value']),
            'pending_orders': processing_orders,
            'revenue_by_date': [
                {
                    'date': item['date'].strftime('%Y-%m-%d'),
                    'total_revenue': float(item['total_revenue'])
                }
                for item in daily_revenue
            ]
        }

        return report_data, None

    @staticmethod
    def generate_all_orders_revenue_report(date_from, date_to, sales_admin=None, delivery_staff=None):
        """
        Generate a revenue report for all orders (excluding cancelled orders) with:
        - Total revenue
        - Total number of orders
        - Average order value
        - Number of pending orders
        - Revenue by date
        """
        # Parse date parameters
        start_date, end_date = ReportRepository.parse_date_params(date_from, date_to)

        if not start_date or not end_date:
            return None, "Invalid date format. Expected format is YYYY-MM-DD."

        # Get base queryset for revenue calculations - exclude cancelled orders
        queryset = ReportRepository.get_base_queryset(
            start_date, end_date, status_filter=['delivered', 'pending', 'processing', 'shipped', 'returned', 'refunded'],
            include_all=False
        )

        # Get processing orders count
        processing_orders = ReportRepository.get_processing_orders_count(start_date, end_date)

        # Filter by sales admin if provided (supports multiple values)
        if sales_admin:
            # Handle comma-separated values
            if ',' in sales_admin:
                sales_admin_ids = [id.strip() for id in sales_admin.split(',') if id.strip()]
                queryset = queryset.filter(sales_admin_id__in=sales_admin_ids)
            else:
                queryset = queryset.filter(sales_admin_id=sales_admin)

        # Filter by delivery staff if provided
        if delivery_staff:
            queryset = queryset.filter(delivery_staff_id=delivery_staff)

        # Calculate aggregates
        aggregates = ReportRepository.get_revenue_aggregates(queryset)

        # Get daily revenue data
        daily_revenue = ReportRepository.get_daily_revenue(queryset)

        # Prepare report data
        report_data = {
            'total_revenue': float(aggregates['total_revenue']),
            'total_orders': aggregates['total_orders'],
            'average_order_value': float(aggregates['avg_order_value']),
            'pending_orders': processing_orders,
            'revenue_by_date': [
                {
                    'date': item['date'].strftime('%Y-%m-%d'),
                    'total_revenue': float(item['total_revenue'])
                }
                for item in daily_revenue
            ]
        }

        return report_data, None

    @staticmethod
    def generate_product_report(date_from, date_to, category_id=None, is_active=None, sales_admin=None):
        """
        Generate a report of product sales for delivered orders only

        Parameters:
        - date_from: Start date (YYYY-MM-DD)
        - date_to: End date (YYYY-MM-DD)
        - category_id: Category ID to filter by
        - is_active: Boolean to filter by product active status
        - sales_admin: Sales admin ID to filter by
        """
        # Get product revenue data for delivered orders only, filtered by sales_admin if provided
        products = ReportRepository.get_product_revenue_data(include_all_orders=False, sales_admin=sales_admin)

        # Filter by date range if provided
        if date_from or date_to:
            start_date, end_date = ReportRepository.parse_date_params(date_from, date_to)

            if not start_date or not end_date:
                return None, "Invalid date format. Expected format is YYYY-MM-DD."

            # We still need to filter by date range, but we don't need to filter by sales_admin again
            # since it's already applied in get_product_revenue_data
            product_ids = ReportRepository.get_product_ids_in_date_range(
                start_date, end_date, sales_admin=None, include_all_orders=False
            )
            products = products.filter(id__in=product_ids)

        # Filter by category if provided
        if category_id:
            products = products.filter(category_id=category_id)

        # Filter by active status if provided
        if is_active is not None:
            is_active_bool = is_active.lower() == 'true' if isinstance(is_active, str) else is_active
            products = products.filter(is_active=is_active_bool)

        # Order by total quantity sold (descending)
        products = products.order_by('-total_quantity')

        return products, None

    @staticmethod
    def generate_all_products_report(date_from, date_to, category_id=None, is_active=None, sales_admin=None):
        """
        Generate a report of product sales for all orders (excluding cancelled orders)

        Parameters:
        - date_from: Start date (YYYY-MM-DD)
        - date_to: End date (YYYY-MM-DD)
        - category_id: Category ID to filter by
        - is_active: Boolean to filter by product active status
        - sales_admin: Sales admin ID to filter by
        """
        # Get product revenue data for all orders except cancelled ones, filtered by sales_admin if provided
        products = ReportRepository.get_product_revenue_data(include_all_orders=True, sales_admin=sales_admin)

        # Filter by date range if provided
        if date_from or date_to:
            start_date, end_date = ReportRepository.parse_date_params(date_from, date_to)

            if not start_date or not end_date:
                return None, "Invalid date format. Expected format is YYYY-MM-DD."

            # We still need to filter by date range, but we don't need to filter by sales_admin again
            # since it's already applied in get_product_revenue_data
            product_ids = ReportRepository.get_product_ids_in_date_range(
                start_date, end_date, sales_admin=None, include_all_orders=True
            )
            products = products.filter(id__in=product_ids)

        # Filter by category if provided
        if category_id:
            products = products.filter(category_id=category_id)

        # Filter by active status if provided
        if is_active is not None:
            is_active_bool = is_active.lower() == 'true' if isinstance(is_active, str) else is_active
            products = products.filter(is_active=is_active_bool)

        # Order by total quantity sold (descending)
        products = products.order_by('-total_quantity')

        return products, None

    @staticmethod
    def generate_customer_revenue_comparison(start_date_str, end_date_str):
        """
        Compare revenue between new and returning customers
        """
        if not start_date_str or not end_date_str:
            return None, "Both start_date and end_date parameters are required."

        start_date, end_date = ReportRepository.parse_date_params(start_date_str, end_date_str)
        if not start_date or not end_date:
            return None, "Invalid date format. Expected format is YYYY-MM-DD."

        # Get orders within date range for revenue calculation
        # Using the same filter as all_orders_revenue for consistency
        orders_in_range = ReportRepository.get_base_queryset(
            start_date, end_date,
            status_filter=['delivered', 'pending', 'processing', 'shipped', 'returned', 'refunded'],
            include_all=False
        ).filter(user__isnull=False)  # Ensure we only count orders with valid users

        # Calculate total revenue per user within the date range
        user_revenue = orders_in_range.values('user_id').annotate(
            total_revenue=Sum(ReportRepository.get_revenue_expression())
        )

        # Get all orders to determine if a customer is new or returning
        all_orders = Order.objects.filter(
            deleted__isnull=True,
            user__isnull=False
        )

        # Count total orders per user
        user_order_counts = all_orders.values('user_id').annotate(
            order_count=Count('id')
        )

        # Convert to dictionary for easier lookup
        user_order_counts_dict = {
            item['user_id']: item['order_count']
            for item in user_order_counts
        }

        # Initialize counters and revenue totals
        new_customers = {
            'count': 0,
            'total_revenue': 0
        }
        returning_customers = {
            'count': 0,
            'total_revenue': 0
        }

        # Categorize customers and sum up their revenue
        for stat in user_revenue:
            total_orders = user_order_counts_dict.get(stat['user_id'], 0)
            if total_orders == 1:
                new_customers['count'] += 1
                new_customers['total_revenue'] += float(stat['total_revenue'])
            else:
                returning_customers['count'] += 1
                returning_customers['total_revenue'] += float(stat['total_revenue'])

        comparison_data = {
            'start_date': start_date.date().isoformat(),
            'end_date': end_date.date().isoformat(),
            'new_customers': new_customers,
            'returning_customers': returning_customers
        }

        return comparison_data, None

    @staticmethod
    def generate_revenue_comparison(filter_by, request_params):
        """
        Generate a revenue comparison report by quarter, month, or year

        Parameters:
        - filter_by: 'quarter' | 'month' | 'year'
        - request_params: Dictionary containing request parameters
        """
        if not filter_by or filter_by not in ['quarter', 'month', 'year']:
            return None, "filter_by parameter is required and must be one of: quarter, month, year"

        current_date = timezone.now().date()

        # Check if date range is provided (for month and quarter views)
        date_from = request_params.get('dateFrom')
        date_to = request_params.get('dateTo')

        # Get base queryset
        if date_from and date_to and filter_by in ['month', 'quarter']:
            start_date, end_date = ReportRepository.parse_date_params(date_from, date_to)
            if not start_date or not end_date:
                return None, "Invalid date format. Expected format is YYYY-MM-DD for 'dateFrom' and 'dateTo'."

            # Get parameter to determine if we should include all orders or just delivered ones
            include_all_orders = request_params.get('include_all_orders', 'false').lower() == 'true'

            # Always use 'delivered' as status_filter for revenue calculations to be consistent
            queryset = ReportRepository.get_base_queryset(
                start_date, end_date, status_filter='delivered', include_all=include_all_orders
            )
        else:
            # Base queryset without date range
            include_all_orders = request_params.get('include_all_orders', 'false').lower() == 'true'

            # Always use 'delivered' as status_filter for revenue calculations to be consistent
            queryset = ReportRepository.get_base_queryset(
                status_filter='delivered', include_all=include_all_orders
            )

        # Process based on filter_by
        if filter_by == 'year':
            return ReportService._process_year_comparison(queryset, request_params)
        elif filter_by == 'month':
            return process_month_comparison(queryset, request_params, current_date, date_from, date_to)
        else:  # quarter
            return process_quarter_comparison(queryset, request_params, current_date, date_from, date_to)

    @staticmethod
    def generate_all_orders_revenue_comparison(filter_by, request_params):
        """
        Generate a revenue comparison report by quarter, month, or year for all orders (excluding cancelled orders)

        Parameters:
        - filter_by: 'quarter' | 'month' | 'year'
        - request_params: Dictionary containing request parameters
        """
        if not filter_by or filter_by not in ['quarter', 'month', 'year']:
            return None, "filter_by parameter is required and must be one of: quarter, month, year"

        current_date = timezone.now().date()

        # Check if date range is provided (for month and quarter views)
        date_from = request_params.get('dateFrom')
        date_to = request_params.get('dateTo')

        # Get base queryset
        if date_from and date_to and filter_by in ['month', 'quarter']:
            start_date, end_date = ReportRepository.parse_date_params(date_from, date_to)
            if not start_date or not end_date:
                return None, "Invalid date format. Expected format is YYYY-MM-DD for 'dateFrom' and 'dateTo'."

            # Get all orders except cancelled ones
            queryset = ReportRepository.get_base_queryset(
                start_date, end_date,
                status_filter=['delivered', 'pending', 'processing', 'shipped', 'returned', 'refunded'],
                include_all=False
            )
        else:
            # Base queryset without date range - all orders except cancelled ones
            queryset = ReportRepository.get_base_queryset(
                status_filter=['delivered', 'pending', 'processing', 'shipped', 'returned', 'refunded'],
                include_all=False
            )

        # Process based on filter_by
        if filter_by == 'year':
            return ReportService._process_year_comparison(queryset, request_params)
        elif filter_by == 'month':
            return process_month_comparison(queryset, request_params, current_date, date_from, date_to)
        else:  # quarter
            return process_quarter_comparison(queryset, request_params, current_date, date_from, date_to)

    @staticmethod
    def _process_year_comparison(queryset, request_params):
        """Process year comparison data"""
        try:
            year_1 = int(request_params.get('year_1'))
            year_2 = int(request_params.get('year_2'))
        except (TypeError, ValueError):
            return None, "year_1 and year_2 parameters are required and must be valid years"

        # Get data for both years
        data = []
        for year in [year_1, year_2]:
            # Use revenue expression helper
            revenue_expr = ReportRepository.get_revenue_expression()
            year_data = queryset.filter(created_at__year=year).aggregate(
                revenue=Coalesce(
                    Sum(revenue_expr),
                    0,
                    output_field=DecimalField(max_digits=12, decimal_places=2)
                ),
                total_products_sold=Coalesce(
                    Sum('items__quantity'),
                    0
                ),
                total_orders=Count('id')  # This is already filtered for delivered orders in the base queryset
            )
            data.append({
                'year': year,
                'revenue': float(year_data['revenue']),
                'total_products_sold': year_data['total_products_sold'],
                'total_orders': year_data['total_orders']
            })

        return {'type': 'year', 'data': data}, None

    @staticmethod
    def generate_top_customers_report(start_date, end_date, limit=10):
        """
        Generate a report of top customers by revenue within a date range

        Parameters:
        - start_date: Start date (YYYY-MM-DD)
        - end_date: End date (YYYY-MM-DD)
        - limit: Number of top customers to return (default: 10)

        Returns:
        - Dictionary with start_date, end_date, and top_customers list
        - Error message if any
        """
        # Parse date parameters
        parsed_start_date, parsed_end_date = ReportRepository.parse_date_params(start_date, end_date)

        if not parsed_start_date or not parsed_end_date:
            return None, "Invalid date format. Expected format is YYYY-MM-DD."

        # Get orders within date range for revenue calculation
        # Using the same filter as all_orders_revenue for consistency
        orders_in_range = ReportRepository.get_base_queryset(
            parsed_start_date, parsed_end_date,
            status_filter=['delivered', 'pending', 'processing', 'shipped', 'returned', 'refunded'],
            include_all=False
        ).filter(user__isnull=False)  # Ensure we only count orders with valid users

        # Get top customers by revenue
        from django.contrib.auth.models import User
        from django.db.models import Sum, Count, F, DecimalField, ExpressionWrapper, Value
        from django.db.models.functions import Coalesce

        # Calculate revenue expression
        revenue_expr = ReportRepository.get_revenue_expression()

        # Calculate total revenue per user within the date range
        user_revenue = orders_in_range.values('user').annotate(
            user_id=F('user'),
            total_revenue=Coalesce(
                Sum(revenue_expr),
                0,
                output_field=DecimalField(max_digits=12, decimal_places=2)
            ),
            total_orders_in_range=Count('id')
        ).order_by('-total_revenue')[:limit]

        # Get all orders to determine if a customer is new or returning
        # Using the same logic as customer_revenue_comparison
        all_orders = Order.objects.filter(
            deleted__isnull=True,
            user__isnull=False
        )

        # Count total orders per user
        user_order_counts = all_orders.values('user').annotate(
            order_count=Count('id')
        )

        # Convert to dictionary for easier lookup
        user_order_counts_dict = {
            item['user']: item['order_count']
            for item in user_order_counts
        }

        # Get user details for the top customers
        user_ids = [item['user_id'] for item in user_revenue]
        users = User.objects.filter(id__in=user_ids)

        # Create a dictionary for quick lookup
        user_dict = {user.id: user for user in users}

        # Prepare the final result
        top_customers = []
        for stat in user_revenue:
            user_id = stat['user_id']
            user = user_dict.get(user_id)

            if user:
                # Get total orders across all time for this user
                total_orders = user_order_counts_dict.get(user_id, 0)

                top_customers.append({
                    'user_id': user_id,
                    'first_name': user.first_name,
                    'email': user.email,
                    'total_orders': total_orders,  # Total orders across all time
                    'total_revenue': float(stat['total_revenue']),
                    'is_new_customer': total_orders == 1  # New customer if only 1 order total
                })

        result = {
            'start_date': parsed_start_date.date().isoformat(),
            'end_date': parsed_end_date.date().isoformat(),
            'top_customers': top_customers
        }

        return result, None

    @staticmethod
    def generate_delivery_revenue_report(date_from, date_to):
        """
        Generate a delivery revenue report showing:
        - Revenue by delivery staff (users with delivery_staff role)
        - Revenue by shipping methods (for other delivery methods)

        Parameters:
        - date_from: Start date (YYYY-MM-DD)
        - date_to: End date (YYYY-MM-DD)

        Returns:
        - List of delivery revenue data with object format:
          [
            {"type": "delivery_staff", "name": "Nguyễn văn A", "revenue": 3500000, "orders": 35},
            {"type": "shipping_method", "name": "Chành xe", "revenue": 200000, "orders": 10}
          ]
        """
        # Parse date parameters
        start_date, end_date = ReportRepository.parse_date_params(date_from, date_to)

        if not start_date or not end_date:
            return None, "Invalid date format. Expected format is YYYY-MM-DD."

        # Get delivery revenue data
        delivery_data = ReportRepository.get_delivery_revenue_data(start_date, end_date, 'delivered')

        # Prepare response data
        revenue_list = []

        # Add delivery staff revenue
        for staff in delivery_data['delivery_staff_revenue']:
            # Get full name or use username as fallback
            first_name = staff.get('delivery_staff__first_name', '') or ''
            last_name = staff.get('delivery_staff__last_name', '') or ''
            username = staff.get('delivery_staff__username', '')

            if first_name and last_name:
                full_name = f"{first_name} {last_name}".strip()
            else:
                full_name = username

            revenue_item = {
                "type": "delivery_staff",
                "name": f"Nhân viên giao hàng : {full_name}",
                "revenue": int(staff['total_revenue']),
                "orders": staff['total_orders']
            }
            revenue_list.append(revenue_item)

        # Add shipping method revenue
        # Mapping shipping unit codes to Vietnamese names
        shipping_unit_names = {
            'company_vehicle': 'Xe Cty',
            'motorbike': 'Xe máy',
            'grab': 'Grab',
            'transport_partner': 'Chành xe',
            'shipping_partner': 'ĐVVC',
        }

        for method in delivery_data['shipping_method_revenue']:
            shipping_unit = method.get('shipping_unit', '')
            display_name = shipping_unit_names.get(shipping_unit, shipping_unit)

            revenue_item = {
                "type": "shipping_method",
                "name": display_name,
                "revenue": int(method['total_revenue']),
                "orders": method['total_orders']
            }
            revenue_list.append(revenue_item)

        return revenue_list, None

    @staticmethod
    def generate_user_stats_report(date_from, date_to, order_by='total_spent_desc', limit='all', role=None):
        """
        Generate a user statistics report showing:
        - User information and stats
        - Total orders and revenue per user
        - Filtered by date range and role
        - Sorted by specified criteria

        Parameters:
        - date_from: Start date (YYYY-MM-DD) - optional
        - date_to: End date (YYYY-MM-DD) - optional
        - order_by: Sort order (total_spent_desc, total_spent_asc, total_orders_desc, total_orders_asc)
        - limit: Number of users to return ('all', 'top10', 'top20', or specific number)
        - role: Filter by user role - optional

        Returns:
        - List of user stats data
        """
        from django.contrib.auth.models import User
        from django.db.models import Count, Sum, Max, Q
        from ...models import Order

        # Parse date parameters if provided
        start_date = None
        end_date = None
        if date_from or date_to:
            start_date, end_date = ReportRepository.parse_date_params(date_from, date_to)
            if (date_from and not start_date) or (date_to and not end_date):
                return None, "Invalid date format. Expected format is YYYY-MM-DD."

        # Start with all users
        users_queryset = User.objects.select_related('profile').all()

        # Filter by role if specified
        if role:
            users_queryset = users_queryset.filter(profile__role=role)

        # Build order filter for date range
        order_filter = Q()
        if start_date and end_date:
            order_filter = Q(orders__created_at__gte=start_date, orders__created_at__lte=end_date)
        elif start_date:
            order_filter = Q(orders__created_at__gte=start_date)
        elif end_date:
            order_filter = Q(orders__created_at__lte=end_date)

        # Annotate users with stats
        if order_filter:
            users_queryset = users_queryset.annotate(
                total_orders=Count('orders', filter=order_filter),
                total_spent=Sum('orders__total_price', filter=order_filter),
                last_order_date=Max('orders__created_at', filter=order_filter)
            )
        else:
            users_queryset = users_queryset.annotate(
                total_orders=Count('orders'),
                total_spent=Sum('orders__total_price'),
                last_order_date=Max('orders__created_at')
            )

        # Filter out users with no orders in the specified period
        users_queryset = users_queryset.filter(total_orders__gt=0)

        # Apply sorting
        if order_by == 'total_spent_desc':
            users_queryset = users_queryset.order_by('-total_spent')
        elif order_by == 'total_spent_asc':
            users_queryset = users_queryset.order_by('total_spent')
        elif order_by == 'total_orders_desc':
            users_queryset = users_queryset.order_by('-total_orders')
        elif order_by == 'total_orders_asc':
            users_queryset = users_queryset.order_by('total_orders')
        else:
            # Default to total_spent_desc
            users_queryset = users_queryset.order_by('-total_spent')

        # Apply limit
        if limit != 'all':
            if limit.startswith('top'):
                try:
                    limit_num = int(limit[3:])  # Extract number from 'top10', 'top20', etc.
                    users_queryset = users_queryset[:limit_num]
                except ValueError:
                    return None, "Invalid limit format. Use 'all', 'top10', 'top20', etc."
            else:
                try:
                    limit_num = int(limit)
                    users_queryset = users_queryset[:limit_num]
                except ValueError:
                    return None, "Invalid limit format. Use 'all', 'top10', or a specific number."

        # Prepare response data
        user_stats = []
        for user in users_queryset:
            user_stats.append({
                'user_id': user.id,
                'user_name': user.get_full_name() or user.username,
                'email': user.email,
                'role': user.profile.role if hasattr(user, 'profile') else 'unknown',
                'total_orders': user.total_orders or 0,
                'total_spent': float(user.total_spent or 0),
                'last_order_date': user.last_order_date.strftime('%Y-%m-%d') if user.last_order_date else None
            })

        return user_stats, None

    @staticmethod
    def generate_same_period_revenue_comparison(filter_by, request_params):
        """
        Generate a same-period revenue comparison report (year-over-year comparison).

        This compares revenue data for the same time periods across different years.
        For example: January 2023 vs January 2024, Q1 2023 vs Q1 2024, etc.

        Parameters:
        - filter_by: 'quarter' | 'month' | 'year'
        - request_params: Dictionary containing request parameters
        """
        if not filter_by or filter_by not in ['quarter', 'month', 'year']:
            return None, "filter_by parameter is required and must be one of: quarter, month, year"

        current_date = timezone.now().date()

        # Get years to compare
        current_year = int(request_params.get('current_year', current_date.year))
        compare_with_year = int(request_params.get('compare_with_year', current_year - 1))

        # Check if date range is provided (for month and quarter views)
        date_from = request_params.get('dateFrom')
        date_to = request_params.get('dateTo')

        if date_from and date_to:
            # Parse date range
            start_date, end_date = ReportRepository.parse_date_params(date_from, date_to)
            if not start_date or not end_date:
                return None, "Invalid date format. Expected format is YYYY-MM-DD."

            # Get base queryset with date range
            current_queryset = ReportRepository.get_base_queryset(
                start_date, end_date, status_filter='delivered', include_all=False
            )

            # Calculate the same period in the previous year
            previous_start_date = start_date.replace(year=compare_with_year)
            previous_end_date = end_date.replace(year=compare_with_year)

            previous_queryset = ReportRepository.get_base_queryset(
                previous_start_date, previous_end_date, status_filter='delivered', include_all=False
            )
        else:
            # Base queryset without date range - filter by years
            current_queryset = ReportRepository.get_base_queryset(
                status_filter='delivered', include_all=False
            ).filter(created_at__year=current_year)

            previous_queryset = ReportRepository.get_base_queryset(
                status_filter='delivered', include_all=False
            ).filter(created_at__year=compare_with_year)

        # Process based on filter_by
        if filter_by == 'year':
            return _process_same_period_year_comparison(
                current_queryset, previous_queryset, current_year, compare_with_year
            )
        elif filter_by == 'month':
            return _process_same_period_month_comparison(
                current_queryset, previous_queryset, current_year, compare_with_year,
                current_date, date_from, date_to
            )
        else:  # quarter
            return _process_same_period_quarter_comparison(
                current_queryset, previous_queryset, current_year, compare_with_year,
                current_date, date_from, date_to
            )

    @staticmethod
    def generate_same_period_all_orders_revenue_comparison(filter_by, request_params):
        """
        Generate a same-period revenue comparison report for all orders (excluding cancelled orders).

        This compares revenue data for the same time periods across different years.

        Parameters:
        - filter_by: 'quarter' | 'month' | 'year'
        - request_params: Dictionary containing request parameters
        """
        if not filter_by or filter_by not in ['quarter', 'month', 'year']:
            return None, "filter_by parameter is required and must be one of: quarter, month, year"

        current_date = timezone.now().date()

        # Get years to compare
        current_year = int(request_params.get('current_year', current_date.year))
        compare_with_year = int(request_params.get('compare_with_year', current_year - 1))

        # Check if date range is provided (for month and quarter views)
        date_from = request_params.get('dateFrom')
        date_to = request_params.get('dateTo')

        if date_from and date_to:
            # Parse date range
            start_date, end_date = ReportRepository.parse_date_params(date_from, date_to)
            if not start_date or not end_date:
                return None, "Invalid date format. Expected format is YYYY-MM-DD."

            # Get base queryset with date range (exclude cancelled orders)
            current_queryset = ReportRepository.get_base_queryset(
                start_date, end_date,
                status_filter=['delivered', 'pending', 'processing', 'shipped', 'returned', 'refunded'],
                include_all=False
            )

            # Calculate the same period in the previous year
            previous_start_date = start_date.replace(year=compare_with_year)
            previous_end_date = end_date.replace(year=compare_with_year)

            previous_queryset = ReportRepository.get_base_queryset(
                previous_start_date, previous_end_date,
                status_filter=['delivered', 'pending', 'processing', 'shipped', 'returned', 'refunded'],
                include_all=False
            )
        else:
            # Base queryset without date range - filter by years (exclude cancelled orders)
            current_queryset = ReportRepository.get_base_queryset(
                status_filter=['delivered', 'pending', 'processing', 'shipped', 'returned', 'refunded'],
                include_all=False
            ).filter(created_at__year=current_year)

            previous_queryset = ReportRepository.get_base_queryset(
                status_filter=['delivered', 'pending', 'processing', 'shipped', 'returned', 'refunded'],
                include_all=False
            ).filter(created_at__year=compare_with_year)

        # Process based on filter_by
        if filter_by == 'year':
            return _process_same_period_year_comparison(
                current_queryset, previous_queryset, current_year, compare_with_year
            )
        elif filter_by == 'month':
            return _process_same_period_month_comparison(
                current_queryset, previous_queryset, current_year, compare_with_year,
                current_date, date_from, date_to
            )
        else:  # quarter
            return _process_same_period_quarter_comparison(
                current_queryset, previous_queryset, current_year, compare_with_year,
                current_date, date_from, date_to
            )
