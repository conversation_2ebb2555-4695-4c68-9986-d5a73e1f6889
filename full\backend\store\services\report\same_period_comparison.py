from django.db.models import Count, Sum, F, DecimalField, ExpressionWrapper
from django.db.models.functions import Coalesce, ExtractMonth
from django.utils import timezone
from ...repositories.report.report_repository import ReportRepository


def _process_same_period_year_comparison(current_queryset, previous_queryset, current_year, compare_with_year):
    """Process same-period year comparison data"""
    
    # Get data for both years
    data = []
    
    # Current year data
    revenue_expr = ReportRepository.get_revenue_expression()
    current_data = current_queryset.aggregate(
        revenue=Coalesce(
            Sum(revenue_expr),
            0,
            output_field=DecimalField(max_digits=12, decimal_places=2)
        ),
        total_products_sold=Coalesce(
            Sum('items__quantity'),
            0
        ),
        total_orders=Count('id')
    )
    
    # Previous year data
    previous_data = previous_queryset.aggregate(
        revenue=Coalesce(
            Sum(revenue_expr),
            0,
            output_field=DecimalField(max_digits=12, decimal_places=2)
        ),
        total_products_sold=Coalesce(
            Sum('items__quantity'),
            0
        ),
        total_orders=Count('id')
    )
    
    # Calculate percentage changes
    revenue_change = _calculate_percentage_change(
        float(previous_data['revenue']), 
        float(current_data['revenue'])
    )
    
    products_change = _calculate_percentage_change(
        previous_data['total_products_sold'], 
        current_data['total_products_sold']
    )
    
    orders_change = _calculate_percentage_change(
        previous_data['total_orders'], 
        current_data['total_orders']
    )
    
    # Format data for response
    data = [
        {
            'year': compare_with_year,
            'revenue': float(previous_data['revenue']),
            'total_products_sold': previous_data['total_products_sold'],
            'total_orders': previous_data['total_orders'],
            'period_type': 'previous'
        },
        {
            'year': current_year,
            'revenue': float(current_data['revenue']),
            'total_products_sold': current_data['total_products_sold'],
            'total_orders': current_data['total_orders'],
            'period_type': 'current',
            'revenue_change': revenue_change,
            'products_change': products_change,
            'orders_change': orders_change
        }
    ]
    
    return {
        'type': 'same_period_year', 
        'data': data,
        'comparison_info': {
            'current_year': current_year,
            'compare_with_year': compare_with_year,
            'revenue_change': revenue_change,
            'products_change': products_change,
            'orders_change': orders_change
        }
    }, None


def _process_same_period_month_comparison(current_queryset, previous_queryset, current_year, compare_with_year, current_date, date_from, date_to):
    """Process same-period month comparison data"""
    
    # Determine which months to include
    if date_from and date_to:
        start_date, end_date = ReportRepository.parse_date_params(date_from, date_to)
        start_month = start_date.month
        end_month = end_date.month
        months_to_include = range(start_month, end_month + 1)
    else:
        # Default behavior: all months up to current month
        months_to_include = range(1, current_date.month + 1)
    
    # Get revenue expression
    revenue_expr = ReportRepository.get_revenue_expression()
    
    # Get current year data by month
    current_monthly_data = current_queryset.annotate(
        month=ExtractMonth('created_at')
    ).values('month').annotate(
        revenue=Coalesce(
            Sum(revenue_expr),
            0,
            output_field=DecimalField(max_digits=12, decimal_places=2)
        ),
        total_products_sold=Coalesce(
            Sum('items__quantity'),
            0
        ),
        total_orders=Count('id')
    ).order_by('month')
    
    # Get previous year data by month
    previous_monthly_data = previous_queryset.annotate(
        month=ExtractMonth('created_at')
    ).values('month').annotate(
        revenue=Coalesce(
            Sum(revenue_expr),
            0,
            output_field=DecimalField(max_digits=12, decimal_places=2)
        ),
        total_products_sold=Coalesce(
            Sum('items__quantity'),
            0
        ),
        total_orders=Count('id')
    ).order_by('month')
    
    # Convert to dictionaries for easier lookup
    current_data_dict = {item['month']: item for item in current_monthly_data}
    previous_data_dict = {item['month']: item for item in previous_monthly_data}
    
    # Build comparison data
    data = []
    for month in months_to_include:
        current_month_data = current_data_dict.get(month, {
            'revenue': 0, 'total_products_sold': 0, 'total_orders': 0
        })
        previous_month_data = previous_data_dict.get(month, {
            'revenue': 0, 'total_products_sold': 0, 'total_orders': 0
        })
        
        # Calculate percentage changes
        revenue_change = _calculate_percentage_change(
            float(previous_month_data['revenue']), 
            float(current_month_data['revenue'])
        )
        
        products_change = _calculate_percentage_change(
            previous_month_data['total_products_sold'], 
            current_month_data['total_products_sold']
        )
        
        orders_change = _calculate_percentage_change(
            previous_month_data['total_orders'], 
            current_month_data['total_orders']
        )
        
        data.append({
            'month': month,
            'current_year': current_year,
            'compare_with_year': compare_with_year,
            'current_revenue': float(current_month_data['revenue']),
            'previous_revenue': float(previous_month_data['revenue']),
            'current_products_sold': current_month_data['total_products_sold'],
            'previous_products_sold': previous_month_data['total_products_sold'],
            'current_orders': current_month_data['total_orders'],
            'previous_orders': previous_month_data['total_orders'],
            'revenue_change': revenue_change,
            'products_change': products_change,
            'orders_change': orders_change
        })
    
    return {
        'type': 'same_period_month', 
        'data': data,
        'comparison_info': {
            'current_year': current_year,
            'compare_with_year': compare_with_year
        }
    }, None


def _process_same_period_quarter_comparison(current_queryset, previous_queryset, current_year, compare_with_year, current_date, date_from, date_to):
    """Process same-period quarter comparison data"""
    
    # Get current quarter
    current_quarter = (current_date.month - 1) // 3 + 1
    
    # Determine which quarters to include
    if date_from and date_to:
        start_date, end_date = ReportRepository.parse_date_params(date_from, date_to)
        start_quarter = (start_date.month - 1) // 3 + 1
        end_quarter = (end_date.month - 1) // 3 + 1
        quarters_to_include = range(start_quarter, end_quarter + 1)
    else:
        quarters_to_include = range(1, current_quarter + 1)
    
    # Get revenue expression
    revenue_expr = ReportRepository.get_revenue_expression()
    
    data = []
    for quarter in quarters_to_include:
        # Calculate start and end months for the quarter
        start_month = (quarter - 1) * 3 + 1
        end_month = quarter * 3
        
        # Get current year quarter data
        current_quarter_orders = current_queryset.filter(
            created_at__month__gte=start_month,
            created_at__month__lte=end_month
        )
        
        current_quarter_data = current_quarter_orders.aggregate(
            revenue=Coalesce(
                Sum(revenue_expr),
                0,
                output_field=DecimalField(max_digits=12, decimal_places=2)
            ),
            total_products_sold=Coalesce(
                Sum('items__quantity'),
                0
            ),
            total_orders=Count('id')
        )
        
        # Get previous year quarter data
        previous_quarter_orders = previous_queryset.filter(
            created_at__month__gte=start_month,
            created_at__month__lte=end_month
        )
        
        previous_quarter_data = previous_quarter_orders.aggregate(
            revenue=Coalesce(
                Sum(revenue_expr),
                0,
                output_field=DecimalField(max_digits=12, decimal_places=2)
            ),
            total_products_sold=Coalesce(
                Sum('items__quantity'),
                0
            ),
            total_orders=Count('id')
        )
        
        # Calculate percentage changes
        revenue_change = _calculate_percentage_change(
            float(previous_quarter_data['revenue']), 
            float(current_quarter_data['revenue'])
        )
        
        products_change = _calculate_percentage_change(
            previous_quarter_data['total_products_sold'], 
            current_quarter_data['total_products_sold']
        )
        
        orders_change = _calculate_percentage_change(
            previous_quarter_data['total_orders'], 
            current_quarter_data['total_orders']
        )
        
        data.append({
            'quarter': quarter,
            'current_year': current_year,
            'compare_with_year': compare_with_year,
            'current_revenue': float(current_quarter_data['revenue']),
            'previous_revenue': float(previous_quarter_data['revenue']),
            'current_products_sold': current_quarter_data['total_products_sold'],
            'previous_products_sold': previous_quarter_data['total_products_sold'],
            'current_orders': current_quarter_data['total_orders'],
            'previous_orders': previous_quarter_data['total_orders'],
            'revenue_change': revenue_change,
            'products_change': products_change,
            'orders_change': orders_change
        })
    
    return {
        'type': 'same_period_quarter', 
        'data': data,
        'comparison_info': {
            'current_year': current_year,
            'compare_with_year': compare_with_year
        }
    }, None


def _calculate_percentage_change(old_value, new_value):
    """Calculate percentage change between two values"""
    if old_value == 0:
        return 100.0 if new_value > 0 else 0.0
    
    return round(((new_value - old_value) / old_value) * 100, 2)
