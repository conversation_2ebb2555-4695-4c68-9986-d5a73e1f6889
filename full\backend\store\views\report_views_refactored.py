from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from ..services.report.report_service import ReportService

class ProductReportPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100

class ReportViewSet(viewsets.ViewSet):
    """
    ViewSet for generating sales and order reports
    """
    permission_classes = [IsAuthenticated]
    pagination_class = ProductReportPagination

    def list(self, request):
        """
        Generate a report containing:
        - Total revenue
        - Total number of orders
        - Average order value
        - Number of pending orders

        Supports filtering by:
        - Date range (dateFrom, dateTo)
        - Sales admin (sales_admin) - supports multiple IDs: sales_admin=1,2,3
        - Delivery staff (delivery_staff)
        """
        # Get parameters
        date_from = request.query_params.get('dateFrom')
        date_to = request.query_params.get('dateTo')
        sales_admin = request.query_params.get('sales_admin')
        delivery_staff = request.query_params.get('delivery_staff')

        # Generate report using service
        report_data, error = ReportService.generate_revenue_report(
            date_from, date_to, sales_admin, delivery_staff
        )

        if error:
            return Response({"error": error}, status=400)

        return Response(report_data)

    @action(detail=False, methods=['get'])
    def all_orders_revenue(self, request):
        """
        Generate a revenue report for all orders (excluding cancelled orders):
        - Total revenue
        - Total number of orders
        - Average order value
        - Number of pending orders
        - Revenue by date

        Supports filtering by:
        - Date range (dateFrom, dateTo)
        - Sales admin (sales_admin) - supports multiple IDs: sales_admin=1,2,3
        - Delivery staff (delivery_staff)
        """
        # Get parameters
        date_from = request.query_params.get('dateFrom')
        date_to = request.query_params.get('dateTo')
        sales_admin = request.query_params.get('sales_admin')
        delivery_staff = request.query_params.get('delivery_staff')

        # Generate report using service
        report_data, error = ReportService.generate_all_orders_revenue_report(
            date_from, date_to, sales_admin, delivery_staff
        )

        if error:
            return Response({"error": error}, status=400)

        return Response(report_data)

    @action(detail=False, methods=['get'])
    def products(self, request):
        """
        Generate a report of product sales for delivered orders only.

        Supports filtering by:
        - Date range (dateFrom, dateTo)
        - Category (category)
        - Product status (is_active)
        - Sales admin (sales_admin) - supports multiple IDs: sales_admin=1,2,3

        Parameters:
        - dateFrom: Start date (YYYY-MM-DD)
        - dateTo: End date (YYYY-MM-DD)
        - category: Category ID
        - is_active: Boolean (true/false)
        - sales_admin: Sales admin ID
        - page: Page number
        - page_size: Number of items per page
        """
        # Initialize pagination
        paginator = self.pagination_class()

        # Get parameters
        date_from = request.query_params.get('dateFrom')
        date_to = request.query_params.get('dateTo')
        category_id = request.query_params.get('category')
        is_active = request.query_params.get('is_active')
        sales_admin = request.query_params.get('sales_admin')

        # Generate product report using service
        products, error = ReportService.generate_product_report(
            date_from, date_to, category_id, is_active, sales_admin
        )

        if error:
            return Response({"error": error}, status=400)

        # Paginate results
        paginated_products = paginator.paginate_queryset(products, request)

        # Prepare response data
        product_data = []
        for product in paginated_products:
            product_data.append({
                'id': product.id,
                'code': product.code,
                'name': product.name,
                'category': product.category.name if product.category else None,
                'price': float(product.price),
                'discount_price': float(product.discount_price) if product.discount_price else None,
                'total_quantity_sold': product.total_quantity,
                'total_revenue': float(product.total_revenue),
                'is_active': product.is_active,
                'stock': product.stock
            })

        return paginator.get_paginated_response(product_data)

    @action(detail=False, methods=['get'])
    def all_products(self, request):
        """
        Generate a report of product sales for all orders (excluding cancelled orders).

        Supports filtering by:
        - Date range (dateFrom, dateTo)
        - Category (category)
        - Product status (is_active)
        - Sales admin (sales_admin) - supports multiple IDs: sales_admin=1,2,3

        Parameters:
        - dateFrom: Start date (YYYY-MM-DD)
        - dateTo: End date (YYYY-MM-DD)
        - category: Category ID
        - is_active: Boolean (true/false)
        - sales_admin: Sales admin ID
        - page: Page number
        - page_size: Number of items per page
        """
        # Initialize pagination
        paginator = self.pagination_class()

        # Get parameters
        date_from = request.query_params.get('dateFrom')
        date_to = request.query_params.get('dateTo')
        category_id = request.query_params.get('category')
        is_active = request.query_params.get('is_active')
        sales_admin = request.query_params.get('sales_admin')

        # Generate product report using service
        products, error = ReportService.generate_all_products_report(
            date_from, date_to, category_id, is_active, sales_admin
        )

        if error:
            return Response({"error": error}, status=400)

        # Paginate results
        paginated_products = paginator.paginate_queryset(products, request)

        # Prepare response data
        product_data = []
        for product in paginated_products:
            product_data.append({
                'id': product.id,
                'code': product.code,
                'name': product.name,
                'category': product.category.name if product.category else None,
                'price': float(product.price),
                'discount_price': float(product.discount_price) if product.discount_price else None,
                'total_quantity_sold': product.total_quantity,
                'total_revenue': float(product.total_revenue),
                'is_active': product.is_active,
                'stock': product.stock
            })

        return paginator.get_paginated_response(product_data)

    @action(detail=False, methods=['get'])
    def customer_revenue_comparison(self, request):
        """
        Compare revenue between new and returning customers.

        New customers: Customers with exactly 1 order total from the beginning until now
        Returning customers: Customers with 2 or more orders total from the beginning until now

        Revenue is calculated for orders within the specified date range, but customer classification
        (new vs returning) is based on their total order count across all time.

        Parameters:
        - start_date: Start date (YYYY-MM-DD)
        - end_date: End date (YYYY-MM-DD)
        """
        # Get parameters
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')

        # Generate comparison using service
        comparison_data, error = ReportService.generate_customer_revenue_comparison(
            start_date_str, end_date_str
        )

        if error:
            return Response({"error": error}, status=400)

        return Response(comparison_data)

    @action(detail=False, methods=['get'])
    def revenue_comparison(self, request):
        """
        Generate a revenue comparison report by quarter, month, or year.

        Parameters:
        - filter_by: 'quarter' | 'month' | 'year'
        - year_1: required if filter_by='year' (int, e.g. 2023)
        - year_2: required if filter_by='year' (int, e.g. 2024)
        - dateFrom: Optional start date for filtering (YYYY-MM-DD) - only used for month and quarter views
        - dateTo: Optional end date for filtering (YYYY-MM-DD) - only used for month and quarter views
        - include_all_orders: Optional boolean parameter (default: false) - If true, includes all orders in counts,
          but revenue calculations will still only include delivered orders for consistency with other APIs
        """
        # Get filter_by parameter
        filter_by = request.query_params.get('filter_by')
        print(f"[DEBUG] Revenue Comparison API - filter_by: {filter_by}")

        # Generate comparison using service
        comparison_data, error = ReportService.generate_revenue_comparison(
            filter_by, request.query_params
        )

        if error:
            return Response({"error": error}, status=400)

        return Response(comparison_data)

    @action(detail=False, methods=['get'])
    def all_orders_revenue_comparison(self, request):
        """
        Generate a revenue comparison report by quarter, month, or year for all orders (excluding cancelled orders).

        Parameters:
        - filter_by: 'quarter' | 'month' | 'year'
        - year_1: required if filter_by='year' (int, e.g. 2023)
        - year_2: required if filter_by='year' (int, e.g. 2024)
        - dateFrom: Optional start date for filtering (YYYY-MM-DD) - only used for month and quarter views
        - dateTo: Optional end date for filtering (YYYY-MM-DD) - only used for month and quarter views
        """
        # Get filter_by parameter
        filter_by = request.query_params.get('filter_by')
        print(f"[DEBUG] All Orders Revenue Comparison API - filter_by: {filter_by}")

        # Generate comparison using service
        comparison_data, error = ReportService.generate_all_orders_revenue_comparison(
            filter_by, request.query_params
        )

        if error:
            return Response({"error": error}, status=400)

        return Response(comparison_data)

    @action(detail=False, methods=['get'])
    def revenue_same_period_comparison(self, request):
        """
        Generate a same-period revenue comparison report (year-over-year comparison).

        This compares revenue data for the same time periods across different years.
        For example: January 2023 vs January 2024, Q1 2023 vs Q1 2024, etc.

        Parameters:
        - filter_by: 'quarter' | 'month' | 'year'
        - current_year: The year to compare (default: current year)
        - compare_with_year: The year to compare against (default: current_year - 1)
        - dateFrom: Optional start date for filtering (YYYY-MM-DD) - only used for month and quarter views
        - dateTo: Optional end date for filtering (YYYY-MM-DD) - only used for month and quarter views
        """
        # Get filter_by parameter
        filter_by = request.query_params.get('filter_by')
        print(f"[DEBUG] Same Period Revenue Comparison API - filter_by: {filter_by}")

        # Generate comparison using service
        comparison_data, error = ReportService.generate_same_period_revenue_comparison(
            filter_by, request.query_params
        )

        if error:
            return Response({"error": error}, status=400)

        return Response(comparison_data)

    @action(detail=False, methods=['get'])
    def all_orders_revenue_same_period_comparison(self, request):
        """
        Generate a same-period revenue comparison report for all orders (excluding cancelled orders).

        This compares revenue data for the same time periods across different years.
        For example: January 2023 vs January 2024, Q1 2023 vs Q1 2024, etc.

        Parameters:
        - filter_by: 'quarter' | 'month' | 'year'
        - current_year: The year to compare (default: current year)
        - compare_with_year: The year to compare against (default: current_year - 1)
        - dateFrom: Optional start date for filtering (YYYY-MM-DD) - only used for month and quarter views
        - dateTo: Optional end date for filtering (YYYY-MM-DD) - only used for month and quarter views
        """
        # Get filter_by parameter
        filter_by = request.query_params.get('filter_by')
        print(f"[DEBUG] Same Period All Orders Revenue Comparison API - filter_by: {filter_by}")

        # Generate comparison using service
        comparison_data, error = ReportService.generate_same_period_all_orders_revenue_comparison(
            filter_by, request.query_params
        )

        if error:
            return Response({"error": error}, status=400)

        return Response(comparison_data)

    @action(detail=False, methods=['get'])
    def top_customers(self, request):
        """
        Get top 10 customers by revenue within a date range.

        Parameters:
        - start_date: Start date (YYYY-MM-DD)
        - end_date: End date (YYYY-MM-DD)
        - limit: Optional number of customers to return (default: 10)

        Returns:
        - List of top customers with their revenue data
        """
        # Get parameters
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        try:
            limit = int(request.query_params.get('limit', 10))
        except (ValueError, TypeError):
            limit = 10

        # Generate report using service
        customers_data, error = ReportService.generate_top_customers_report(
            start_date, end_date, limit
        )

        if error:
            return Response({"error": error}, status=400)

        return Response(customers_data)

    @action(detail=False, methods=['get'])
    def delivery_revenue(self, request):
        """
        Get revenue report by delivery staff and shipping methods.

        Returns revenue data for:
        - Delivery staff (users with delivery_staff role)
        - Other shipping methods (for orders not handled by delivery staff)

        Parameters:
        - dateFrom: Start date (YYYY-MM-DD)
        - dateTo: End date (YYYY-MM-DD)

        Returns:
        - List of strings with format:
          ["Nhân viên giao hàng : Nguyễn văn A, 3500000, 35", "Chành xe, 200000, 10"]
        """
        # Get parameters
        date_from = request.query_params.get('dateFrom')
        date_to = request.query_params.get('dateTo')

        # Generate report using service
        revenue_data, error = ReportService.generate_delivery_revenue_report(
            date_from, date_to
        )

        if error:
            return Response({"error": error}, status=400)

        return Response({"delivery_revenue": revenue_data})

    @action(detail=False, methods=['get'])
    def user_stats(self, request):
        """
        Get user statistics report with revenue and order data.

        Parameters:
        - dateFrom: Start date (YYYY-MM-DD) - optional
        - dateTo: End date (YYYY-MM-DD) - optional
        - order_by: Sort order (total_spent_desc, total_spent_asc, total_orders_desc, total_orders_asc) - default: total_spent_desc
        - limit: Number of users to return (default: all, use 'top10', 'top20', etc. or specific number)
        - role: Filter by user role (customer, sales_admin, etc.) - optional

        Returns:
        - List of users with their stats:
          [
            {
              "user_id": 1,
              "user_name": "Nguyễn Văn A",
              "email": "<EMAIL>",
              "role": "customer",
              "total_orders": 15,
              "total_spent": 2500000,
              "last_order_date": "2024-01-15"
            }
          ]
        """
        # Get parameters
        date_from = request.query_params.get('dateFrom')
        date_to = request.query_params.get('dateTo')
        order_by = request.query_params.get('order_by', 'total_spent_desc')
        limit = request.query_params.get('limit', 'all')
        role = request.query_params.get('role')

        # Generate report using service
        user_stats_data, error = ReportService.generate_user_stats_report(
            date_from, date_to, order_by, limit, role
        )

        if error:
            return Response({"error": error}, status=400)

        return Response({"user_stats": user_stats_data})
