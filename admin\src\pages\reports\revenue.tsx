import React, { useState, useEffect, useCallback } from "react";
import { Select, Table, DatePicker, Tabs, Switch, Space, Typography } from "antd";
import { Banknote, BanknoteIcon, TrendingUp, TrendingDown } from "lucide-react";

const { Text } = Typography;
import { apiCall, endpoints } from "@/lib/api"; // Giả định import đúng
import moment from "moment"; // Đảm bảo đã cài moment

// Định nghĩa kiểu dữ liệu cho từng mục trong data array
interface RevenueDataItem {
  month?: number;
  quarter?: number;
  year?: number;
  revenue: number;
  total_products_sold: number;
  total_orders: number;
}

// Kiểu dữ liệu cho response từ API
interface ApiResponse {
  type: FilterType;
  data: RevenueDataItem[];
}

// Kiểu dữ liệu đã chuyển đổi cho bảng
interface RevenueData {
  period: string;
  revenue: number;
  totalProducts: number;
  totalOrders: number;
  // For same-period comparison
  previousRevenue?: number;
  previousProducts?: number;
  previousOrders?: number;
  revenueChange?: number;
  productsChange?: number;
  ordersChange?: number;
}

// Kiểu dữ liệu cho same-period comparison response
interface SamePeriodComparisonItem {
  month?: number;
  quarter?: number;
  year?: number;
  current_year?: number;
  compare_with_year?: number;
  current_revenue: number;
  previous_revenue: number;
  current_products_sold: number;
  previous_products_sold: number;
  current_orders: number;
  previous_orders: number;
  revenue_change: number;
  products_change: number;
  orders_change: number;
}

interface SamePeriodApiResponse {
  type: string;
  data: SamePeriodComparisonItem[];
  comparison_info: {
    current_year: number;
    compare_with_year: number;
    revenue_change?: number;
    products_change?: number;
    orders_change?: number;
  };
}

type FilterType = "month" | "quarter" | "year";

const RevenueReport: React.FC = () => {
  const [activeTab, setActiveTab] = useState<"actual" | "all">("actual");
  const [filterType, setFilterType] = useState<FilterType>("month");
  const [year1, setYear1] = useState<string | undefined>(undefined);
  const [year2, setYear2] = useState<string | undefined>(undefined);
  const [enableSamePeriodComparison, setEnableSamePeriodComparison] = useState<boolean>(false);
  const [currentYear, setCurrentYear] = useState<string>(new Date().getFullYear().toString());
  const [compareWithYear, setCompareWithYear] = useState<string>((new Date().getFullYear() - 1).toString());

  // Separate data states for actual and all revenue
  const [actualData, setActualData] = useState<RevenueData[]>([]);
  const [allData, setAllData] = useState<RevenueData[]>([]);

  // Current data based on active tab
  const data = activeTab === "actual" ? actualData : allData;

  // Separate loading and error states
  const [actualLoading, setActualLoading] = useState<boolean>(false);
  const [allLoading, setAllLoading] = useState<boolean>(false);
  const [actualError, setActualError] = useState<string | null>(null);
  const [allError, setAllError] = useState<string | null>(null);

  // Current loading and error states based on active tab
  const loading = activeTab === "actual" ? actualLoading : allLoading;
  const error = activeTab === "actual" ? actualError : allError;

  // --- Logic Fetch Actual Revenue Data ---
  const fetchActualData = useCallback(async () => {
    // Validation is now handled in the useEffect
    setActualLoading(true);
    setActualError(null);
    let url = `${endpoints.reports.compareRevenue}?filter_by=${filterType}`;

    if (filterType === "year" && year1 && year2) {
      url += `&year_1=${year1}&year_2=${year2}`;
    }

    console.log("Fetching actual revenue data with URL:", url);

    try {
      // Sử dụng kiểu ApiResponse đã định nghĩa
      const response = await apiCall<ApiResponse>("GET", url);

      // Kiểm tra và chuyển đổi dữ liệu từ API
      if (response && Array.isArray(response.data)) {
        const transformedData: RevenueData[] = response.data.map((item) => {
          let period = "";
          if (item.month) period = `Tháng ${item.month}`;
          else if (item.quarter) period = `Quý ${item.quarter}`;
          else if (item.year) period = `Năm ${item.year}`;

          return {
            period,
            revenue: item.revenue,
            totalProducts: item.total_products_sold,
            totalOrders: item.total_orders || 0,
          };
        });

        setActualData(transformedData);
        console.log("Actual revenue data set successfully:", transformedData);
      } else {
        console.warn("Invalid API response structure or no data:", response);
        setActualData([]);
        setActualError("Dữ liệu trả về không hợp lệ.");
      }
    } catch (err: any) {
      console.error("Error fetching actual revenue data:", err);
      setActualError(`Lỗi khi tải dữ liệu: ${err.message || "Unknown error"}`);
      setActualData([]);
    } finally {
      setActualLoading(false);
    }
  }, [filterType, year1, year2]);

  // --- Logic Fetch All Revenue Data (including cancelled orders) ---
  const fetchAllData = useCallback(async () => {
    // Validation is now handled in the useEffect
    setAllLoading(true);
    setAllError(null);
    let url = `${endpoints.reports.compareRevenueAll}?filter_by=${filterType}`;

    if (filterType === "year" && year1 && year2) {
      url += `&year_1=${year1}&year_2=${year2}`;
    }

    console.log("Fetching all revenue data with URL:", url);

    try {
      const response = await apiCall<ApiResponse>("GET", url);

      if (response && Array.isArray(response.data)) {
        const transformedData: RevenueData[] = response.data.map((item) => {
          let period = "";
          if (item.month) period = `Tháng ${item.month}`;
          else if (item.quarter) period = `Quý ${item.quarter}`;
          else if (item.year) period = `Năm ${item.year}`;

          return {
            period,
            revenue: item.revenue,
            totalProducts: item.total_products_sold,
            totalOrders: item.total_orders || 0,
          };
        });

        setAllData(transformedData);
        console.log("All revenue data set successfully:", transformedData);
      } else {
        console.warn("Invalid API response structure or no data:", response);
        setAllData([]);
        setAllError("Dữ liệu trả về không hợp lệ.");
      }
    } catch (err: any) {
      console.error("Error fetching all revenue data:", err);
      setAllError(`Lỗi khi tải dữ liệu: ${err.message || "Unknown error"}`);
      setAllData([]);
    } finally {
      setAllLoading(false);
    }
  }, [filterType, year1, year2]);

  // --- useEffect để gọi fetch functions khi dependencies thay đổi ---
  useEffect(() => {
    if (enableSamePeriodComparison) {
      // For same-period comparison, we need current year and compare with year
      if (!currentYear || !compareWithYear) {
        return;
      }

      // Fetch same-period comparison data
      fetchSamePeriodActualData();
      fetchSamePeriodAllData();

      console.log(
        `Fetching same-period comparison data with filter: ${filterType}, currentYear: ${currentYear}, compareWithYear: ${compareWithYear}`
      );
    } else {
      // Original logic for regular comparison
      // Only fetch data when filterType is valid and if it's "year", both years must be selected
      if (filterType === "year" && (!year1 || !year2)) {
        return;
      }

      // Fetch both types of data
      fetchActualData();
      fetchAllData();

      // Log for debugging
      console.log(
        `Fetching data with filter: ${filterType}, year1: ${year1}, year2: ${year2}`
      );
    }
  }, [
    filterType,
    year1,
    year2,
    enableSamePeriodComparison,
    currentYear,
    compareWithYear,
    fetchActualData,
    fetchAllData,
    fetchSamePeriodActualData,
    fetchSamePeriodAllData
  ]);

  // --- Logic Fetch Same Period Comparison Data ---
  const fetchSamePeriodActualData = useCallback(async () => {
    setActualLoading(true);
    setActualError(null);
    let url = `${endpoints.reports.samePeriodCompareRevenue}?filter_by=${filterType}`;

    if (currentYear && compareWithYear) {
      url += `&current_year=${currentYear}&compare_with_year=${compareWithYear}`;
    }

    console.log("Fetching same period actual revenue data with URL:", url);

    try {
      const response = await apiCall<SamePeriodApiResponse>("GET", url);

      if (response && Array.isArray(response.data)) {
        const transformedData: RevenueData[] = response.data.map((item) => {
          let period = "";
          if (item.month) period = `Tháng ${item.month}`;
          else if (item.quarter) period = `Quý ${item.quarter}`;
          else if (item.year) period = `Năm ${item.year}`;

          return {
            period,
            revenue: item.current_revenue,
            totalProducts: item.current_products_sold,
            totalOrders: item.current_orders,
            previousRevenue: item.previous_revenue,
            previousProducts: item.previous_products_sold,
            previousOrders: item.previous_orders,
            revenueChange: item.revenue_change,
            productsChange: item.products_change,
            ordersChange: item.orders_change,
          };
        });

        setActualData(transformedData);
        console.log("Same period actual revenue data set successfully:", transformedData);
      } else {
        console.warn("Invalid API response structure or no data:", response);
        setActualData([]);
        setActualError("Dữ liệu trả về không hợp lệ.");
      }
    } catch (err: any) {
      console.error("Error fetching same period actual revenue data:", err);
      setActualError(`Lỗi khi tải dữ liệu: ${err.message || "Unknown error"}`);
      setActualData([]);
    } finally {
      setActualLoading(false);
    }
  }, [filterType, currentYear, compareWithYear]);

  const fetchSamePeriodAllData = useCallback(async () => {
    setAllLoading(true);
    setAllError(null);
    let url = `${endpoints.reports.samePeriodCompareRevenueAll}?filter_by=${filterType}`;

    if (currentYear && compareWithYear) {
      url += `&current_year=${currentYear}&compare_with_year=${compareWithYear}`;
    }

    console.log("Fetching same period all revenue data with URL:", url);

    try {
      const response = await apiCall<SamePeriodApiResponse>("GET", url);

      if (response && Array.isArray(response.data)) {
        const transformedData: RevenueData[] = response.data.map((item) => {
          let period = "";
          if (item.month) period = `Tháng ${item.month}`;
          else if (item.quarter) period = `Quý ${item.quarter}`;
          else if (item.year) period = `Năm ${item.year}`;

          return {
            period,
            revenue: item.current_revenue,
            totalProducts: item.current_products_sold,
            totalOrders: item.current_orders,
            previousRevenue: item.previous_revenue,
            previousProducts: item.previous_products_sold,
            previousOrders: item.previous_orders,
            revenueChange: item.revenue_change,
            productsChange: item.products_change,
            ordersChange: item.orders_change,
          };
        });

        setAllData(transformedData);
        console.log("Same period all revenue data set successfully:", transformedData);
      } else {
        console.warn("Invalid API response structure or no data:", response);
        setAllData([]);
        setAllError("Dữ liệu trả về không hợp lệ.");
      }
    } catch (err: any) {
      console.error("Error fetching same period all revenue data:", err);
      setAllError(`Lỗi khi tải dữ liệu: ${err.message || "Unknown error"}`);
      setAllData([]);
    } finally {
      setAllLoading(false);
    }
  }, [filterType, currentYear, compareWithYear]);

  // --- Handler cho việc thay đổi Filter ---
  const handleFilterChange = (value: FilterType) => {
    setFilterType(value);
    // Reset năm khi không còn filter theo năm
    if (value !== "year") {
      setYear1(undefined);
      setYear2(undefined);
    }
    // No need to call API here - the useEffect will handle it
  };

  // --- Handler cho Same Period Comparison ---
  const handleSamePeriodToggle = (checked: boolean) => {
    setEnableSamePeriodComparison(checked);
    // Reset year selections when toggling
    if (checked) {
      setYear1(undefined);
      setYear2(undefined);
    }
  };

  // --- Handler cho DatePicker ---
  const handleYear1Change = (date: moment.Moment | null) => {
    setYear1(date ? date.format("YYYY") : undefined);
    // No need to call API here - the useEffect will handle it
  };

  const handleYear2Change = (date: moment.Moment | null) => {
    setYear2(date ? date.format("YYYY") : undefined);
    // No need to call API here - the useEffect will handle it
  };

  // --- Tính toán Max Revenue và Max Orders ---
  // Thêm kiểm tra data có phần tử không trước khi dùng Math.max
  const maxRevenue =
    data.length > 0 ? Math.max(...data.map((item) => item.revenue)) : 0;
  const maxOrders =
    data.length > 0 ? Math.max(...data.map((item) => item.totalOrders)) : 0;

  // --- Render Hàm Doanh Thu ---
  const renderRevenue = (revenue: number | undefined | null) => {
    // Kiểm tra revenue có phải là số hợp lệ không
    if (typeof revenue !== "number" || isNaN(revenue)) {
      return <span>-</span>; // Hoặc giá trị mặc định khác
    }
    return (
      <span>
        {new Intl.NumberFormat("vi-VN", {
          style: "currency",
          currency: "VND",
          minimumFractionDigits: 0,
        }).format(revenue)}{" "}
        {revenue > 0 ? (
          <BanknoteIcon className="inline-block w-4 h-4 text-green-500" />
        ) : (
          // Hiển thị icon đỏ cho cả số 0 hoặc số âm
          <Banknote className="inline-block w-4 h-4 text-red-500" />
        )}
      </span>
    );
  };

  // --- Render Percentage Change ---
  const renderPercentageChange = (change: number | undefined) => {
    if (typeof change !== "number" || isNaN(change)) {
      return <span>-</span>;
    }

    const isPositive = change > 0;
    const isNegative = change < 0;

    return (
      <span className={`flex items-center gap-1 ${
        isPositive ? 'text-green-600' : isNegative ? 'text-red-600' : 'text-gray-600'
      }`}>
        {isPositive && <TrendingUp className="w-3 h-3" />}
        {isNegative && <TrendingDown className="w-3 h-3" />}
        {change.toFixed(1)}%
      </span>
    );
  };

  // --- Tạo Columns động cho Bảng ---
  const columns = [
    {
      title: "Chỉ số", // Đổi tên cho rõ ràng hơn
      dataIndex: "metric",
      key: "metric",
      fixed: true as const, // Đảm bảo kiểu 'fixed' là literal type
      width: 200, // Có thể set width cố định cho cột đầu
    },
    // Chỉ tạo cột khi có data
    ...(data.length > 0
      ? data.map((item) => {
          if (enableSamePeriodComparison) {
            // For same-period comparison, create multiple columns per period
            return [
              {
                title: `${item.period} ${currentYear}`,
                dataIndex: `${item.period}_current`,
                key: `${item.period}_current`,
                align: "right" as const,
                render: (value: any, record: { metric: string }) => {
                  if (record.metric === "Doanh thu") {
                    return renderRevenue(value);
                  }
                  if (
                    (record.metric === "Tổng sản phẩm bán được" ||
                      record.metric === "Tổng đơn hàng") &&
                    typeof value === "number"
                  ) {
                    return new Intl.NumberFormat("vi-VN").format(value);
                  }
                  return value ?? "-";
                },
              },
              {
                title: `${item.period} ${compareWithYear}`,
                dataIndex: `${item.period}_previous`,
                key: `${item.period}_previous`,
                align: "right" as const,
                render: (value: any, record: { metric: string }) => {
                  if (record.metric === "Doanh thu") {
                    return renderRevenue(value);
                  }
                  if (
                    (record.metric === "Tổng sản phẩm bán được" ||
                      record.metric === "Tổng đơn hàng") &&
                    typeof value === "number"
                  ) {
                    return new Intl.NumberFormat("vi-VN").format(value);
                  }
                  return value ?? "-";
                },
              },
              {
                title: `Thay đổi (%)`,
                dataIndex: `${item.period}_change`,
                key: `${item.period}_change`,
                align: "center" as const,
                render: (value: any) => renderPercentageChange(value),
              },
            ];
          } else {
            // Original single column per period
            return {
              title: item.period,
              dataIndex: item.period,
              key: item.period,
              align: "right" as const,
              render: (value: any, record: { metric: string }) => {
                if (record.metric === "Doanh thu") {
                  return renderRevenue(value);
                }
                if (
                  (record.metric === "Tổng sản phẩm bán được" ||
                    record.metric === "Tổng đơn hàng") &&
                  typeof value === "number"
                ) {
                  return new Intl.NumberFormat("vi-VN").format(value);
                }
                return value ?? "-";
              },
            };
          }
        }).flat()
      : []), // Trả về mảng rỗng nếu chưa có data
  ];

  // --- Tạo DataSource cho Bảng ---
  const dataSource =
    data.length > 0
      ? [
          {
            key: "revenue_row",
            metric: "Doanh thu",
            ...data.reduce((acc, item) => {
              if (enableSamePeriodComparison) {
                acc[`${item.period}_current`] = item.revenue;
                acc[`${item.period}_previous`] = item.previousRevenue || 0;
                acc[`${item.period}_change`] = item.revenueChange;
              } else {
                acc[item.period] = item.revenue;
              }
              return acc;
            }, {} as { [key: string]: number }),
          },
          {
            key: "orders_row",
            metric: "Tổng đơn hàng",
            ...data.reduce((acc, item) => {
              if (enableSamePeriodComparison) {
                acc[`${item.period}_current`] = item.totalOrders;
                acc[`${item.period}_previous`] = item.previousOrders || 0;
                acc[`${item.period}_change`] = item.ordersChange;
              } else {
                acc[item.period] = item.totalOrders;
              }
              return acc;
            }, {} as { [key: string]: number }),
          },
          {
            key: "products_row",
            metric: "Tổng sản phẩm bán được",
            ...data.reduce((acc, item) => {
              if (enableSamePeriodComparison) {
                acc[`${item.period}_current`] = item.totalProducts;
                acc[`${item.period}_previous`] = item.previousProducts || 0;
                acc[`${item.period}_change`] = item.productsChange;
              } else {
                acc[item.period] = item.totalProducts;
              }
              return acc;
            }, {} as { [key: string]: number }),
          },
        ]
      : [];

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Báo cáo doanh thu</h1>

      {/* Tabs for switching between actual and all revenue */}
      <div className="mb-4">
        <Tabs
          activeKey={activeTab}
          onChange={(key) => {
            // Just update the tab state - no need to fetch data again
            setActiveTab(key as "actual" | "all");
          }}
          items={[
            {
              key: "actual",
              label: "Doanh thu thực tế",
              children: (
                <div className="text-sm text-gray-500 mt-2">
                  Hiển thị doanh thu thực tế
                </div>
              ),
            },
            {
              key: "all",
              label: "Doanh thu tổng",
              children: (
                <div className="text-sm text-gray-500 mt-2">
                  Hiển thị tổng doanh thu (bao gồm cả đơn hàng đã hủy và đang xử
                  lí)
                </div>
              ),
            },
          ]}
        />
      </div>

      {/* Phần Filter */}
      <div className="mb-6 space-y-4">
        <div className="flex flex-wrap gap-4 items-center">
          <Select
            style={{ width: 150 }}
            value={filterType}
            onChange={handleFilterChange}
            options={[
              { label: "Theo tháng", value: "month" },
              { label: "Theo quý", value: "quarter" },
              { label: "Theo năm", value: "year" },
            ]}
          />

          {/* Same Period Comparison Toggle */}
          <Space>
            <Switch
              checked={enableSamePeriodComparison}
              onChange={handleSamePeriodToggle}
            />
            <Text>So sánh cùng kỳ</Text>
          </Space>

          {/* Hiển thị trạng thái loading hoặc lỗi */}
          {loading && <span className="ml-4">Đang tải dữ liệu...</span>}
          {error && <span className="ml-4 text-red-600">{error}</span>}
        </div>

        {/* Year selection for regular comparison */}
        {!enableSamePeriodComparison && filterType === "year" && (
          <div className="flex flex-wrap gap-4 items-center">
            <DatePicker
              picker="year"
              placeholder="Chọn năm 1"
              onChange={handleYear1Change}
              value={year1 ? moment(year1, 'YYYY') : null}
            />
            <DatePicker
              picker="year"
              placeholder="Chọn năm 2"
              onChange={handleYear2Change}
              value={year2 ? moment(year2, 'YYYY') : null}
            />
          </div>
        )}

        {/* Year selection for same-period comparison */}
        {enableSamePeriodComparison && (
          <div className="flex flex-wrap gap-4 items-center">
            <Space>
              <Text>Năm hiện tại:</Text>
              <DatePicker
                picker="year"
                placeholder="Chọn năm hiện tại"
                value={currentYear ? moment(currentYear, 'YYYY') : null}
                onChange={(date) => setCurrentYear(date ? date.format("YYYY") : new Date().getFullYear().toString())}
              />
            </Space>
            <Space>
              <Text>So sánh với:</Text>
              <DatePicker
                picker="year"
                placeholder="Chọn năm so sánh"
                value={compareWithYear ? moment(compareWithYear, 'YYYY') : null}
                onChange={(date) => setCompareWithYear(date ? date.format("YYYY") : (new Date().getFullYear() - 1).toString())}
              />
            </Space>
          </div>
        )}
      </div>

      {/* Bảng Dữ liệu */}
      <div className="relative">
        <div className="flex justify-between items-center mb-4">
          <div className="flex gap-2">
            {activeTab === "all" && (
              <div className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                Doanh thu tổng
              </div>
            )}
            {enableSamePeriodComparison && (
              <div className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                So sánh cùng kỳ: {currentYear} vs {compareWithYear}
              </div>
            )}
          </div>
        </div>
        <Table
          columns={columns}
          dataSource={dataSource}
          rowKey="key" // Sử dụng key đã thêm vào dataSource
          loading={loading} // Sử dụng state loading của Antd Table
          scroll={{ x: "max-content" }} // Giữ scroll ngang
          bordered // Thêm viền cho dễ nhìn
          pagination={false} // Tắt phân trang nếu không cần
          // Row class name logic để highlight dòng có giá trị cao nhất
          rowClassName={(record) => {
            if (record.metric === "Doanh thu") {
              // Kiểm tra xem có giá trị nào trong record (ngoại trừ metric và key) bằng maxRevenue không
              const hasMaxRevenue = Object.entries(record)
                .filter(
                  ([key, value]) =>
                    key !== "metric" &&
                    key !== "key" &&
                    typeof value === "number"
                )
                .some(
                  ([_, value]) =>
                    typeof value === "number" &&
                    typeof maxRevenue === "number" &&
                    value === maxRevenue &&
                    maxRevenue > 0
                ); // Chỉ highlight nếu max > 0

              if (hasMaxRevenue) {
                return "bg-[#e6f7ff] font-semibold"; // Thêm cả font-semibold
              }
            }

            if (record.metric === "Tổng đơn hàng") {
              // Kiểm tra xem có giá trị nào trong record (ngoại trừ metric và key) bằng maxOrders không
              const hasMaxOrders = Object.entries(record)
                .filter(
                  ([key, value]) =>
                    key !== "metric" &&
                    key !== "key" &&
                    typeof value === "number"
                )
                .some(
                  ([_, value]) =>
                    typeof value === "number" &&
                    typeof maxOrders === "number" &&
                    value === maxOrders &&
                    maxOrders > 0
                ); // Chỉ highlight nếu max > 0

              if (hasMaxOrders) {
                return "bg-[#f6ffed] font-semibold"; // Màu xanh nhạt cho đơn hàng
              }
            }

            return "";
          }}
        />
      </div>
    </div>
  );
};

export default RevenueReport;
