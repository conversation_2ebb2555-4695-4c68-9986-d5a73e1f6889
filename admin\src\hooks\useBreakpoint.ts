import { useState, useEffect } from 'react';

// Breakpoints matching Tailwind's default breakpoints
const breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

export type Breakpoint = keyof typeof breakpoints;

export function useBreakpoint(breakpoint: Breakpoint): boolean {
  const [isAboveBreakpoint, setIsAboveBreakpoint] = useState(
    window.innerWidth >= breakpoints[breakpoint]
  );

  useEffect(() => {
    const checkSize = () => {
      setIsAboveBreakpoint(window.innerWidth >= breakpoints[breakpoint]);
    };

    window.addEventListener('resize', checkSize);
    checkSize(); // Check on mount

    return () => window.removeEventListener('resize', checkSize);
  }, [breakpoint]);

  return isAboveBreakpoint;
}

export function useIsMobile(): boolean {
  const [isMobile, setIsMobile] = useState(window.innerWidth < breakpoints.sm);

  useEffect(() => {
    const checkSize = () => {
      setIsMobile(window.innerWidth < breakpoints.sm);
    };

    window.addEventListener('resize', checkSize);
    checkSize(); // Check on mount

    return () => window.removeEventListener('resize', checkSize);
  }, []);

  return isMobile;
}
