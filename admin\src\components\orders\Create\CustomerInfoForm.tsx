import React from "react";
import {
  Form,
  Input,
  Card,
  Button,
  Space,
  Typography,
  Divider,
  Select,
  Spin,
} from "antd";
import { CloseOutlined, UserOutlined } from "@ant-design/icons";
import { Customer } from "../../../types/customer";
import { useLocationSelect } from "../../../hooks/useLocationSelect";
import { CustomerRankBadge } from "../../customers/CustomerRankBadge";

const { Title, Text } = Typography;

interface CustomerInfoFormProps {
  selectedCustomer: Customer | null;
  isCreatingNewCustomer: boolean;
  newCustomerForm: {
    first_name: string;
    last_name: string;
    phone_number: string;
    shipping_address: string;
    ward: string;
    district: string;
    city: string;
    creator_id: number;
  };
  orderForm: {
    phone_number: string;
    email: string;
    shipping_address: string;
    ward: string;
    district: string;
    city: string;
    payment_method: "cod" | "cash" | "bank_transfer";
    payment_status: "paid" | "unpaid";
    company_payment_received: boolean;
    user?: number;
  };
  onNewCustomerFormChange: (field: string, value: string) => void;
  onOrderFormChange: (field: string, value: any) => void;
  onSelectCustomerClick: () => void;
  onRemoveCustomer: () => void;
  onCreateNewCustomerClick: () => void;
  onCancelCreateNewCustomer: () => void;
}

const CustomerInfoForm: React.FC<CustomerInfoFormProps> = ({
  selectedCustomer,
  isCreatingNewCustomer,
  newCustomerForm,
  orderForm,
  onNewCustomerFormChange,
  onOrderFormChange,
  onSelectCustomerClick,
  onRemoveCustomer,
  onCreateNewCustomerClick,
  onCancelCreateNewCustomer,
}) => {
  return (
    <Card>
      <div className="flex justify-between items-center">
        <Title level={4} className="m-0">
          Thông tin khách hàng
        </Title>
        {!isCreatingNewCustomer && (
          <Button
            type={selectedCustomer ? "link" : "primary"}
            onClick={onSelectCustomerClick}
          >
            {selectedCustomer ? "Đổi khách hàng" : "Chọn khách hàng"}
          </Button>
        )}
      </div>
      {isCreatingNewCustomer ? (
        <Form layout="vertical">
          <div className="flex justify-between items-center mb-4">
            <Title level={5} className="m-0">
              Tạo khách hàng mới
            </Title>
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={onCancelCreateNewCustomer}
            />
          </div>

          <Form.Item label="Tên" required>
            <Input
              placeholder="Nhập tên"
              value={newCustomerForm.first_name}
              onChange={(e) =>
                onNewCustomerFormChange("first_name", e.target.value)
              }
            />
          </Form.Item>

          <Form.Item label="Số điện thoại" required>
            <Input
              placeholder="Nhập số điện thoại"
              value={newCustomerForm.phone_number}
              onChange={(e) =>
                onNewCustomerFormChange("phone_number", e.target.value)
              }
            />
          </Form.Item>

          <Form.Item label="Địa chỉ giao hàng" required>
            <Input.TextArea
              rows={3}
              placeholder="Nhập địa chỉ giao hàng"
              value={newCustomerForm.shipping_address}
              onChange={(e) =>
                onNewCustomerFormChange("shipping_address", e.target.value)
              }
            />
          </Form.Item>

          <NewCustomerLocationSelect
            newCustomerForm={newCustomerForm}
            onNewCustomerFormChange={onNewCustomerFormChange}
          />
        </Form>
      ) : selectedCustomer ? (
        <Form layout="vertical">
          <Card
            type="inner"
            className="mb-4 bg-blue-50"
            extra={
              <Button
                type="text"
                danger
                icon={<CloseOutlined />}
                onClick={onRemoveCustomer}
                title="Xóa khách hàng"
              />
            }
          >
            <Space direction="vertical" size={0}>
              <div className="flex items-center gap-2">
                <Text strong>
                  {selectedCustomer.first_name} {selectedCustomer.last_name}
                </Text>
                <CustomerRankBadge
                  rank={selectedCustomer.rank || 'normal'}
                  size="small"
                />
              </div>
              <Text type="secondary">{selectedCustomer.email}</Text>
              <Text type="secondary">
                {selectedCustomer.profile.phone_number}
              </Text>
            </Space>
          </Card>

          <Form.Item label="Số điện thoại" required>
            <Input
              readOnly
              value={orderForm.phone_number}
              className="bg-gray-100"
            />
          </Form.Item>

          <Form.Item label="Email">
            <Input
              readOnly
              value={orderForm.email}
              className="bg-gray-100"
            />
          </Form.Item>

          <Form.Item label="Địa chỉ giao hàng" required>
            <Input.TextArea
              rows={3}
              value={orderForm.shipping_address}
              onChange={(e) =>
                onOrderFormChange("shipping_address", e.target.value)
              }
            />
          </Form.Item>

          <ExistingCustomerLocationSelect
            orderForm={orderForm}
            onOrderFormChange={onOrderFormChange}
          />
        </Form>
      ) : (
        <div className="text-center py-8">
          <Space direction="vertical" align="center">
            <UserOutlined className="text-[48px] text-gray-300" />
            <Text type="secondary">Chưa có thông tin khách hàng</Text>
          </Space>
        </div>
      )}
    </Card>
  );
};

interface NewCustomerLocationSelectProps {
  newCustomerForm: {
    ward: string;
    district: string;
    city: string;
  };
  onNewCustomerFormChange: (field: string, value: string) => void;
}

const NewCustomerLocationSelect: React.FC<NewCustomerLocationSelectProps> = ({
  newCustomerForm,
  onNewCustomerFormChange,
}) => {
  const {
    cities,
    districts,
    wards,
    selectedCity,
    selectedDistrict,
    selectedWard,
    loadingCities,
    loadingDistricts,
    loadingWards,
    handleCityChange,
    handleDistrictChange,
    handleWardChange,
  } = useLocationSelect({
    // Don't pass initial values - we'll use empty selects for new customers
    onCityChange: (_, label) => onNewCustomerFormChange("city", label),
    onDistrictChange: (_, label) => onNewCustomerFormChange("district", label),
    onWardChange: (_, label) => onNewCustomerFormChange("ward", label),
  });

  return (
    <div className="flex flex-col">
      <Form.Item label="Thành phố" required>
        <Select
          placeholder="Chọn thành phố"
          value={selectedCity || undefined}
          onChange={handleCityChange}
          loading={loadingCities}
          showSearch
          filterOption={(input, option) =>
            (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
          }
          options={cities}
          className="w-full"
        />
      </Form.Item>

      <Form.Item label="Quận/Huyện" required>
        <Select
          placeholder="Chọn quận/huyện"
          value={selectedDistrict || undefined}
          onChange={handleDistrictChange}
          loading={loadingDistricts}
          disabled={!selectedCity}
          showSearch
          filterOption={(input, option) =>
            (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
          }
          options={districts}
          className="w-full"
        />
      </Form.Item>

      <Form.Item label="Phường/Xã" required>
        <Select
          placeholder="Chọn phường/xã"
          value={selectedWard || undefined}
          onChange={handleWardChange}
          loading={loadingWards}
          disabled={!selectedDistrict}
          showSearch
          filterOption={(input, option) =>
            (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
          }
          options={wards}
          className="w-full"
        />
      </Form.Item>
    </div>
  );
};

interface ExistingCustomerLocationSelectProps {
  orderForm: {
    ward: string;
    district: string;
    city: string;
  };
  onOrderFormChange: (field: string, value: string) => void;
}

const ExistingCustomerLocationSelect: React.FC<
  ExistingCustomerLocationSelectProps
> = ({ orderForm, onOrderFormChange }) => {
  // For existing customers, use text inputs instead of select dropdowns
  return (
    <div className="flex flex-col">
      <Form.Item label="Thành phố">
        <Input
          value={orderForm.city}
          onChange={(e) => onOrderFormChange("city", e.target.value)}
          placeholder="Nhập thành phố"
        />
      </Form.Item>

      <Form.Item label="Quận/Huyện">
        <Input
          value={orderForm.district}
          onChange={(e) => onOrderFormChange("district", e.target.value)}
          placeholder="Nhập quận/huyện"
        />
      </Form.Item>

      <Form.Item label="Phường/Xã">
        <Input
          value={orderForm.ward}
          onChange={(e) => onOrderFormChange("ward", e.target.value)}
          placeholder="Nhập phường/xã"
        />
      </Form.Item>
    </div>
  );
};

export default CustomerInfoForm;
