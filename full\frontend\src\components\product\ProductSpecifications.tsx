import React from "react";

interface ProductSpecificationsProps {
  specifications: string;
}

export const ProductSpecifications: React.FC<ProductSpecificationsProps> = ({ specifications }) => {
  if (!specifications || specifications.trim() === "") {
    return (
      <div className="py-8 text-center text-gray-500">
        <PERSON><PERSON><PERSON> có thông tin đặc điểm sản phẩm
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="whitespace-pre-line text-gray-700 leading-relaxed">
        {specifications}
      </div>
    </div>
  );
};

export default ProductSpecifications;
