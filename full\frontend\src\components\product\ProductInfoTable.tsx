import React from "react";

interface ProductInfoTableProps {
  description: string;
}

export const ProductInfoTable: React.FC<ProductInfoTableProps> = ({ description }) => {
  // Helper function to check if description is in table format
  const isDescriptionTableFormat = (description: string): boolean => {
    if (!description) return false;

    // Check if at least one line matches the format "number. key: value"
    // or at least one line matches "number. section" with content following it
    const lines = description.split('\n');
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line.match(/^\d+\./) && (line.includes(':') || (i < lines.length - 1 && lines[i + 1].trim()))) {
        return true;
      }
    }
    return false;
  };

  // Helper function to get content for a section
  const getSectionContent = (description: string, startIndex: number): string => {
    const lines = description.split('\n');
    const contentLines: string[] = [];

    for (let i = startIndex + 1; i < lines.length; i++) {
      const line = lines[i];
      // Stop if we hit another numbered section
      if (line.match(/^\d+\./)) break;
      if (line.trim()) contentLines.push(line.trim());
    }

    return contentLines.join('\n');
  };

  if (!description) {
    return (
      <div className="py-8 text-center text-gray-500">
        Chưa có thông tin sản phẩm
      </div>
    );
  }

  return (
    <div className="overflow-hidden">
      {isDescriptionTableFormat(description) ? (
        <table className="w-full border-collapse">
          <tbody>
            {description.split('\n').map((line: string, index: number) => {
              if (!line.trim()) return null;

              const parts = line.split(':');
              if (parts.length === 2) {
                // Handle simple key-value pairs
                const [key, value] = parts.map(part => part.trim());
                if (key.match(/^\d+\./)) { // Remove numbering if present
                  const cleanKey = key.replace(/^\d+\./, '').trim();
                  return (
                    <tr key={index} className="border-b border-black last:border-b-0">
                      <td className="font-bold bg-gray-100 text-black p-4 w-1/4 border border-black">
                        {cleanKey}
                      </td>
                      <td className="p-4 text-black bg-white border border-black">
                        {value}
                      </td>
                    </tr>
                  );
                }
              } else if (line.match(/^\d+\./)) {
                // Handle headers without values (e.g., "6. Mô Tả Sản Phẩm")
                const header = line.replace(/^\d+\./, '').trim();
                const content = getSectionContent(description, index);
                if (content) {
                  return (
                    <tr key={index} className="border-b border-black last:border-b-0">
                      <td className="font-bold bg-gray-100 text-black p-4 w-1/4 border border-black">
                        {header}
                      </td>
                      <td className="p-4 text-black bg-white border border-black">
                        <div className="whitespace-pre-line">
                          {content.split('\n').map((paragraph, pIndex) => (
                            <p key={pIndex} className="mb-2 last:mb-0">{paragraph}</p>
                          ))}
                        </div>
                      </td>
                    </tr>
                  );
                }
              }
              return null;
            })}
          </tbody>
        </table>
      ) : (
        <div className="py-4 text-black whitespace-pre-line">
          {description}
        </div>
      )}
    </div>
  );
};

export default ProductInfoTable;
