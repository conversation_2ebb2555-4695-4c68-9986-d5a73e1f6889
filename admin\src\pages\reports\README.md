# Revenue Reports Module

## Overview
This module provides comprehensive revenue analysis and reporting capabilities with support for both regular comparisons and same-period (year-over-year) comparisons.

## Key Components

### Revenue Report (`revenue.tsx`)
The main revenue analysis page that supports:
- **Regular Revenue Analysis**: View revenue data by month, quarter, or year
- **Same-Period Comparison**: Compare revenue data for the same time periods across different years
- **Dual Revenue Types**: Actual revenue (delivered orders only) vs All revenue (all orders except cancelled)

## Features

### 1. Regular Revenue Analysis
- Filter by month, quarter, or year
- Compare two specific years (for year filter)
- View actual revenue vs total revenue
- Interactive data table with highlighting for maximum values

### 2. Same-Period Comparison (New Feature)
- **Year-over-Year Analysis**: Compare the same periods across different years
  - Example: January 2023 vs January 2024
  - Example: Q1 2023 vs Q1 2024
- **Percentage Change Indicators**: Visual indicators showing growth or decline
- **Flexible Year Selection**: Choose any two years for comparison
- **Comprehensive Metrics**: Revenue, orders, and products sold comparisons

### 3. Data Visualization
- **Trend Indicators**: Up/down arrows with color coding
- **Percentage Changes**: Calculated and displayed for all metrics
- **Responsive Tables**: Mobile-friendly data presentation
- **Currency Formatting**: Vietnamese Dong formatting with appropriate icons

## API Integration

### Endpoints
- `GET /reports/revenue_comparison/` - Regular revenue comparison
- `GET /reports/all_orders_revenue_comparison/` - All orders revenue comparison
- `GET /reports/revenue_same_period_comparison/` - Same-period revenue comparison (NEW)
- `GET /reports/all_orders_revenue_same_period_comparison/` - Same-period all orders comparison (NEW)

### Parameters
#### Regular Comparison
- `filter_by`: 'month' | 'quarter' | 'year'
- `year_1`, `year_2`: Required for year filter
- `dateFrom`, `dateTo`: Optional date range

#### Same-Period Comparison
- `filter_by`: 'month' | 'quarter' | 'year'
- `current_year`: Year to analyze (default: current year)
- `compare_with_year`: Year to compare against (default: previous year)
- `dateFrom`, `dateTo`: Optional date range

## Usage Examples

### Enable Same-Period Comparison
1. Toggle the "So sánh cùng kỳ" switch
2. Select the current year and comparison year
3. Choose filter type (month/quarter/year)
4. View side-by-side comparison with percentage changes

### Regular Analysis
1. Keep "So sánh cùng kỳ" disabled
2. Select filter type
3. For year filter, select two years to compare
4. View standard comparison table

## Data Structure

### Regular Revenue Data
```typescript
interface RevenueData {
  period: string;
  revenue: number;
  totalProducts: number;
  totalOrders: number;
}
```

### Same-Period Comparison Data
```typescript
interface RevenueData {
  period: string;
  revenue: number;
  totalProducts: number;
  totalOrders: number;
  // Additional fields for comparison
  previousRevenue?: number;
  previousProducts?: number;
  previousOrders?: number;
  revenueChange?: number;
  productsChange?: number;
  ordersChange?: number;
}
```

## Technical Implementation

### Frontend Components
- **Switch Control**: Toggle between regular and same-period comparison
- **Dynamic Columns**: Table columns adapt based on comparison mode
- **Percentage Renderers**: Custom components for displaying changes
- **Year Selectors**: Separate controls for different comparison modes

### Backend Services
- **ReportService**: Main service class with new same-period methods
- **SamePeriodComparison**: Helper module for processing comparison logic
- **API Views**: New endpoints in `report_views_refactored.py`

### Key Functions
- `_process_same_period_year_comparison()`: Year-over-year analysis
- `_process_same_period_month_comparison()`: Month-over-month analysis
- `_process_same_period_quarter_comparison()`: Quarter-over-quarter analysis
- `_calculate_percentage_change()`: Percentage change calculation

## Benefits

1. **Enhanced Analysis**: Compare performance across equivalent time periods
2. **Trend Identification**: Easily spot growth or decline patterns
3. **Business Intelligence**: Make data-driven decisions based on historical comparisons
4. **User-Friendly**: Intuitive interface with clear visual indicators
5. **Flexible**: Support for different time periods and comparison ranges

## Future Enhancements

- Export functionality for comparison data
- Chart visualizations for trend analysis
- Automated insights and recommendations
- Custom date range comparisons
- Multi-year trend analysis
