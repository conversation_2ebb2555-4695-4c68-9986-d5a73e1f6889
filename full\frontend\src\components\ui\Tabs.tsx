import React, { useState } from "react";
import { cn } from "@/lib/utils";

export interface TabItem {
  id: string;
  label: string;
  content: React.ReactNode;
}

export interface TabsProps {
  tabs: TabItem[];
  defaultTab?: string;
  className?: string;
  tabClassName?: string;
  contentClassName?: string;
}

export const Tabs: React.FC<TabsProps> = ({
  tabs,
  defaultTab,
  className,
  tabClassName,
  contentClassName,
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id);

  const activeTabContent = tabs.find(tab => tab.id === activeTab)?.content;

  return (    
  <div className={cn("w-full", className)}>
      {/* Tab Navigation */}
      <div
        className="flex flex-wrap border-b border-gray-200 relative"
        role="tablist"
      >
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={cn(
              "px-6 py-3 text-sm font-medium rounded-t-xl transition-all duration-300 relative z-10",
              "border border-gray-200 border-b-0 -mr-px",
              activeTab === tab.id
                ? "bg-gradient-to-r from-[#1e478e] via-[#005baa] to-[#0074bc] text-white shadow-sm z-20"
                : "bg-blue-50 text-gray-500 hover:text-black hover:bg-blue-100",
              tabClassName
            )}
          >
            {tab.label}
          </button>
        ))}
      </div>      {/* Tab Content */}
      <div
        className={cn(
          "bg-white border border-gray-200 border-t-0 rounded-b-xl p-6 min-h-[200px] transition-all duration-300",
          contentClassName
        )}
      >
        {activeTabContent}
      </div>
    </div>
  );
};

export default Tabs;
